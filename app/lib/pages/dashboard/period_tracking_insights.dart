import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:auto_route/auto_route.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../custom_widgets/dotted_border_widget.dart';
import 'package:account_management/account_management.dart';
import '../../helpers.dart';

@RoutePage()
class PeriodTrackingInsightsPage extends StatelessWidget {
  const PeriodTrackingInsightsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ManagePeriodTrackingBloc>(
            create: (context) => getIt<ManagePeriodTrackingBloc>()),
        BlocProvider<PeriodTrackingWatcherBloc>(
          create: (context) => getIt<PeriodTrackingWatcherBloc>()
            ..add(PeriodTrackingWatcherEvent.watchAllStarted()),
        ),
      ],
      child: PeriodTrackingEditScaffold(),
    );
  }
}

class PeriodTrackingEditScaffold extends StatefulWidget {
  const PeriodTrackingEditScaffold({super.key});

  @override
  State<PeriodTrackingEditScaffold> createState() =>
      _PeriodTrackingEditScaffoldState();
}

class _PeriodTrackingEditScaffoldState
    extends State<PeriodTrackingEditScaffold> {
  bool _isEditMode = false;

  // Generate list of months to display (past 1 year + current month + future 6 months)
  List<DateTime> _getMonthsToDisplay() {
    final now = DateTime.now();
    List<DateTime> months = [];

    // Add past 12 months
    for (int i = 12; i >= 1; i--) {
      months.add(DateTime(now.year, now.month - i, 1));
    }

    // Add current month
    months.add(DateTime(now.year, now.month, 1));

    // Add future 6 months
    for (int i = 1; i <= 6; i++) {
      months.add(DateTime(now.year, now.month + i, 1));
    }

    return months;
  }

  // Helper method to check if a date is in a period window (continuous flow between period dates)
  bool _isInPeriodWindow(DateTime date, Set<DateTime> periodDates) {
    if (periodDates.isEmpty) return false;

    List<DateTime> sortedPeriodDates = periodDates.toList()..sort();

    // Group consecutive period dates into cycles
    List<List<DateTime>> periodCycles = [];
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedPeriodDates.length; i++) {
      if (currentCycle.isEmpty) {
        currentCycle.add(sortedPeriodDates[i]);
      } else {
        DateTime lastDate = currentCycle.last;
        int daysDifference = sortedPeriodDates[i].difference(lastDate).inDays;

        if (daysDifference <= 1) {
          // Only consecutive days (1 day apart) for continuous flow
          currentCycle.add(sortedPeriodDates[i]);
        } else {
          // Start new cycle
          periodCycles.add(List.from(currentCycle));
          currentCycle = [sortedPeriodDates[i]];
        }
      }
    }
    if (currentCycle.isNotEmpty) {
      periodCycles.add(currentCycle);
    }

    // Check if date falls within any period cycle (between first and last date of consecutive dates)
    for (List<DateTime> cycle in periodCycles) {
      if (cycle.length >= 3) {
        // Only create flow if there are at least 3 consecutive dates
        DateTime startDate = cycle.first;
        DateTime endDate = cycle.last;

        if (date.isAfter(startDate) &&
            date.isBefore(endDate) &&
            !cycle.any((periodDate) =>
                periodDate.year == date.year &&
                periodDate.month == date.month &&
                periodDate.day == date.day)) {
          return true;
        }
      }
    }
    return false;
  }

  // Helper method to check if a date is the first date in a period cycle
  bool _isFirstPeriodDate(DateTime date, Set<DateTime> periodDates) {
    if (periodDates.isEmpty) return false;

    List<DateTime> sortedPeriodDates = periodDates.toList()..sort();

    // Group consecutive period dates into cycles
    List<List<DateTime>> periodCycles = [];
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedPeriodDates.length; i++) {
      if (currentCycle.isEmpty) {
        currentCycle.add(sortedPeriodDates[i]);
      } else {
        DateTime lastDate = currentCycle.last;
        int daysDifference = sortedPeriodDates[i].difference(lastDate).inDays;

        if (daysDifference <= 1) {
          currentCycle.add(sortedPeriodDates[i]);
        } else {
          periodCycles.add(List.from(currentCycle));
          currentCycle = [sortedPeriodDates[i]];
        }
      }
    }
    if (currentCycle.isNotEmpty) {
      periodCycles.add(currentCycle);
    }

    // Check if date is the first date in any cycle
    for (List<DateTime> cycle in periodCycles) {
      DateTime firstDate = cycle.first;
      if (firstDate.year == date.year &&
          firstDate.month == date.month &&
          firstDate.day == date.day) {
        return true;
      }
    }
    return false;
  }

  // Helper method to check if a date is the last date in a period cycle
  bool _isLastPeriodDate(DateTime date, Set<DateTime> periodDates) {
    if (periodDates.isEmpty) return false;

    List<DateTime> sortedPeriodDates = periodDates.toList()..sort();

    // Group consecutive period dates into cycles
    List<List<DateTime>> periodCycles = [];
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedPeriodDates.length; i++) {
      if (currentCycle.isEmpty) {
        currentCycle.add(sortedPeriodDates[i]);
      } else {
        DateTime lastDate = currentCycle.last;
        int daysDifference = sortedPeriodDates[i].difference(lastDate).inDays;

        if (daysDifference <= 1) {
          currentCycle.add(sortedPeriodDates[i]);
        } else {
          periodCycles.add(List.from(currentCycle));
          currentCycle = [sortedPeriodDates[i]];
        }
      }
    }
    if (currentCycle.isNotEmpty) {
      periodCycles.add(currentCycle);
    }

    // Check if date is the last date in any cycle
    for (List<DateTime> cycle in periodCycles) {
      DateTime lastDate = cycle.last;
      if (lastDate.year == date.year &&
          lastDate.month == date.month &&
          lastDate.day == date.day) {
        return true;
      }
    }
    return false;
  }

  // Helper method to check if a date is in an ovulation window (fertile window around ovulation)
  bool _isInOvulationWindow(DateTime date, Set<DateTime> ovulationDates) {
    for (DateTime ovulationDate in ovulationDates) {
      // Check if date is within 2 days before or after an ovulation date
      final difference = date.difference(ovulationDate).inDays.abs();
      if (difference <= 2 && difference > 0) {
        return true;
      }
    }
    return false;
  }

  // Helper method to check if a date is the first date in an ovulation cycle
  bool _isFirstOvulationDate(DateTime date, Set<DateTime> ovulationDates) {
    if (ovulationDates.isEmpty) return false;

    List<DateTime> sortedOvulationDates = ovulationDates.toList()..sort();

    // Group consecutive ovulation dates into cycles
    List<List<DateTime>> ovulationCycles = [];
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedOvulationDates.length; i++) {
      if (currentCycle.isEmpty) {
        currentCycle.add(sortedOvulationDates[i]);
      } else {
        DateTime lastDate = currentCycle.last;
        int daysDifference =
            sortedOvulationDates[i].difference(lastDate).inDays;

        if (daysDifference <= 1) {
          currentCycle.add(sortedOvulationDates[i]);
        } else {
          ovulationCycles.add(List.from(currentCycle));
          currentCycle = [sortedOvulationDates[i]];
        }
      }
    }
    if (currentCycle.isNotEmpty) {
      ovulationCycles.add(currentCycle);
    }

    // Check if date is the first date in any cycle
    for (List<DateTime> cycle in ovulationCycles) {
      DateTime firstDate = cycle.first;
      if (firstDate.year == date.year &&
          firstDate.month == date.month &&
          firstDate.day == date.day) {
        return true;
      }
    }
    return false;
  }

  // Helper method to check if a date is the last date in an ovulation cycle
  bool _isLastOvulationDate(DateTime date, Set<DateTime> ovulationDates) {
    if (ovulationDates.isEmpty) return false;

    List<DateTime> sortedOvulationDates = ovulationDates.toList()..sort();

    // Group consecutive ovulation dates into cycles
    List<List<DateTime>> ovulationCycles = [];
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedOvulationDates.length; i++) {
      if (currentCycle.isEmpty) {
        currentCycle.add(sortedOvulationDates[i]);
      } else {
        DateTime lastDate = currentCycle.last;
        int daysDifference =
            sortedOvulationDates[i].difference(lastDate).inDays;

        if (daysDifference <= 1) {
          currentCycle.add(sortedOvulationDates[i]);
        } else {
          ovulationCycles.add(List.from(currentCycle));
          currentCycle = [sortedOvulationDates[i]];
        }
      }
    }
    if (currentCycle.isNotEmpty) {
      ovulationCycles.add(currentCycle);
    }

    // Check if date is the last date in any cycle
    for (List<DateTime> cycle in ovulationCycles) {
      DateTime lastDate = cycle.last;
      if (lastDate.year == date.year &&
          lastDate.month == date.month &&
          lastDate.day == date.day) {
        return true;
      }
    }
    return false;
  }

  Widget _buildMonthCalendar(DateTime month, PeriodTrackingWatcherState state) {
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        child: Container(
            margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  spreadRadius: 2,
                  blurRadius: 5,
                  offset: Offset(0, 3),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.only(
                  top: 16.h, left: 16.w, right: 16.w, bottom: 20.h),
              child: TableCalendar<DateTime>(
                firstDay: DateTime.utc(2020, 1, 1),
                lastDay: DateTime.utc(2030, 12, 31),
                focusedDay: month,
                calendarFormat: CalendarFormat.month,
                startingDayOfWeek: StartingDayOfWeek.sunday,
                availableGestures:
                    AvailableGestures.none, // Disable all swipe gestures
                headerStyle: HeaderStyle(
                  formatButtonVisible: false,
                  titleCentered: true,
                  leftChevronVisible: false,
                  rightChevronVisible: false,
                  titleTextStyle: GoogleFonts.roboto(
                    fontSize: 30.sp,
                    fontWeight: FontWeight.w400,
                    color: Color(0xff6C618B),
                  ),
                ),
                daysOfWeekStyle: DaysOfWeekStyle(
                  weekdayStyle: GoogleFonts.roboto(
                    fontSize: 25.sp,
                    fontWeight: FontWeight.w500,
                    color: Color(0xff71456F),
                  ),
                  weekendStyle: GoogleFonts.roboto(
                    fontSize: 25.sp,
                    fontWeight: FontWeight.w500,
                    color: Color(0xff71456F),
                  ),
                ),
                onDaySelected: _isEditMode
                    ? (selectedDay, focusedDay) {
                        // Only allow editing past and current dates
                        final today = DateTime.now();
                        final selectedDateOnly = DateTime(selectedDay.year,
                            selectedDay.month, selectedDay.day);
                        final todayOnly =
                            DateTime(today.year, today.month, today.day);

                        if (selectedDateOnly.isAfter(todayOnly)) {
                          // Future date - show message and don't allow selection
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Cannot edit future dates'),
                              backgroundColor: Colors.orange,
                            ),
                          );
                          return;
                        }

                        // Toggle period date selection
                        context.read<ManagePeriodTrackingBloc>().add(
                            ManagePeriodTrackingEvent.selectDay(
                                selectedDay, true));
                      }
                    : null,
                calendarStyle: CalendarStyle(
                  outsideDaysVisible: false,
                  defaultTextStyle: GoogleFonts.roboto(
                    fontSize: 25.sp,
                    fontWeight: FontWeight.w400,
                    color: Color(0xff71456F),
                  ),
                  weekendTextStyle: GoogleFonts.roboto(
                    fontSize: 25.sp,
                    fontWeight: FontWeight.w400,
                    color: Color(0xff71456F),
                  ),
                ),
                calendarBuilders: CalendarBuilders(
                  defaultBuilder: (context, date, focusedDay) {
                    return _buildDayCell(date, state);
                  },
                  todayBuilder: (context, date, focusedDay) {
                    return _buildDayCell(date, state, isToday: true);
                  },
                  outsideBuilder: (context, date, focusedDay) {
                    return Container(); // Hide outside days
                  },
                ),
              ),
            )));
  }

  Widget _buildDayCell(DateTime date, PeriodTrackingWatcherState state,
      {bool isToday = false}) {
    return state.maybeMap(
      data: (dataState) {
        final today = DateTime.now();
        final todayNormalized = DateTime(today.year, today.month, today.day);
        final dateNormalized = DateTime(date.year, date.month, date.day);

        // Check basic date properties
        bool isTodayDate = dateNormalized.isAtSameMomentAs(todayNormalized);
        bool isFutureDate = dateNormalized.isAfter(todayNormalized);

        // Check period-related properties
        bool isSelectedPeriodDate = dataState.selectedDays.any((selectedDay) =>
            selectedDay.year == date.year &&
            selectedDay.month == date.month &&
            selectedDay.day == date.day);
        bool isFirstPeriodDate =
            _isFirstPeriodDate(date, dataState.selectedDays);
        bool isLastPeriodDate = _isLastPeriodDate(date, dataState.selectedDays);
        bool isInPeriodWindow = _isInPeriodWindow(date, dataState.selectedDays);

        // Check ovulation-related properties
        bool isOvulationDate = dataState.ovulationDays.any((ovulationDay) =>
            ovulationDay.year == date.year &&
            ovulationDay.month == date.month &&
            ovulationDay.day == date.day);
        bool isFirstOvulationDate =
            _isFirstOvulationDate(date, dataState.ovulationDays);
        bool isLastOvulationDate =
            _isLastOvulationDate(date, dataState.ovulationDays);
        bool isInOvulationWindow =
            _isInOvulationWindow(date, dataState.ovulationDays);

        // Build the appropriate widget using modular methods
        Widget dayWidget = _buildDayWidget(
          date: date,
          isTodayDate: isTodayDate,
          isFutureDate: isFutureDate,
          isSelectedPeriodDate: isSelectedPeriodDate,
          isFirstPeriodDate: isFirstPeriodDate,
          isLastPeriodDate: isLastPeriodDate,
          isInPeriodWindow: isInPeriodWindow,
          isOvulationDate: isOvulationDate,
          isFirstOvulationDate: isFirstOvulationDate,
          isLastOvulationDate: isLastOvulationDate,
          isInOvulationWindow: isInOvulationWindow,
        );

        return dayWidget;
      },
      orElse: () => Container(
        width: 55.w,
        height: 55.h,
        child: Center(
          child: Text(
            '${date.day}',
            style: GoogleFonts.roboto(
              color: Color(0xff71456F),
              fontWeight: FontWeight.w400,
              fontSize: 25.sp,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      listener: (context, state) {
        state.maybeWhen(
          dataLoaded: () {
            // Period dates saved successfully
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Period dates updated successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            setState(() {
              _isEditMode = false;
            });
          },
          orElse: () {},
        );
      },
      child: Scaffold(
        backgroundColor: Color(0xffFAF2DF),
        appBar: AppBar(
          title: Text('Edit Period Insights'),
          backgroundColor: Color(0xffFAF2DF),
          elevation: 0,
          actions: [
            IconButton(
              icon: Icon(Icons.visibility),
              onPressed: () {
                // Navigate to view page
                context.router.push(PeriodTrackingViewRoute());
              },
            ),
          ],
        ),
        body:
            BlocBuilder<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
          builder: (context, state) {
            return state.maybeMap(
              data: (dataState) {
                final months = _getMonthsToDisplay();
                return Column(
                  children: [
                    Expanded(
                      child: ListView.builder(
                        itemCount: months.length,
                        itemBuilder: (context, index) {
                          return _buildMonthCalendar(months[index], state);
                        },
                      ),
                    ),
                    // Edit button at the bottom
                    Padding(
                      padding: EdgeInsets.all(16.w),
                      child: GestureDetector(
                        onTap: () {
                          if (_isEditMode) {
                            // Save changes and exit edit mode
                            context.read<ManagePeriodTrackingBloc>().add(
                                  ManagePeriodTrackingEvent
                                      .savePeriodDatesAndRecalculate(),
                                );
                            setState(() {
                              _isEditMode = false;
                            });
                          } else {
                            // Enter edit mode
                            setState(() {
                              _isEditMode = true;
                            });
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Tap on dates to select/deselect period days'),
                                backgroundColor: AppTheme.primaryColor,
                              ),
                            );
                          }
                        },
                        child: Container(
                          height: 45.h,
                          width: 100.w,
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            borderRadius: BorderRadius.circular(22.5),
                          ),
                          child: Center(
                            child: Text(
                              _isEditMode ? 'Done' : 'Edit',
                              style: GoogleFonts.roboto(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                                fontSize: 16.sp,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
              orElse: () => Center(child: CircularProgressIndicator()),
            );
          },
        ),
      ),
    );
  }

  // Main widget builder that uses modular helper methods
  Widget _buildDayWidget({
    required DateTime date,
    required bool isTodayDate,
    required bool isFutureDate,
    required bool isSelectedPeriodDate,
    required bool isFirstPeriodDate,
    required bool isLastPeriodDate,
    required bool isInPeriodWindow,
    required bool isOvulationDate,
    required bool isFirstOvulationDate,
    required bool isLastOvulationDate,
    required bool isInOvulationWindow,
  }) {
    // Priority order: Today > Period dates > Ovulation dates > Windows > Regular

    if (isTodayDate) {
      return _buildTodayDate(date);
    }

    // Period dates (first/last have priority over middle)
    if (isFirstPeriodDate) {
      return _buildPeriodFirstDate(date, isFutureDate);
    }
    if (isLastPeriodDate) {
      return _buildPeriodLastDate(date, isFutureDate);
    }
    if (isSelectedPeriodDate) {
      return _buildPeriodMiddleDate(date, isFutureDate);
    }

    // Ovulation dates (first/last have priority over middle)
    if (isFirstOvulationDate) {
      return _buildOvulationFirstDate(date, isFutureDate);
    }
    if (isLastOvulationDate) {
      return _buildOvulationLastDate(date, isFutureDate);
    }
    if (isOvulationDate) {
      return _buildOvulationMiddleDate(date, isFutureDate);
    }

    // Regular date
    return _buildRegularDate(date);
  }

  // Modular helper methods for better code organization

  Widget _buildTodayDate(DateTime date) {
    return Container(
      width: 55.w,
      height: 55.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Color(0xff5DADE2), width: 2),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff5DADE2),
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodFirstDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            left: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(27.5.h),
                  bottomLeft: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xff584294),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xff584294), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodLastDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the left
          Positioned(
            right: 15.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(27.5.h),
                  bottomRight: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xff584294),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xff584294), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodMiddleDate(DateTime date, bool isFuture) {
    return Container(
      width: 104.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: isFuture ? Color(0xff71456F) : Colors.white,
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildOvulationFirstDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            left: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(27.5.h),
                  bottomLeft: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: _buildRegularDate(date),
          ),
        ],
      ),
    );
  }

  Widget _buildOvulationLastDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            right: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(27.5.h),
                  bottomRight: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: _buildRegularDate(date),
          ),
        ],
      ),
    );
  }

  Widget _buildOvulationMiddleDate(DateTime date, bool isFuture) {
    return Container(
      width: 104.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff71456F),
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildCircleContent(
      DateTime date, Color backgroundColor, Color textColor) {
    return Container(
      width: 60.w,
      height: 60.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor,
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: textColor,
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildWindowDate(DateTime date, Color backgroundColor) {
    return Container(
      width: 55.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff71456F),
            fontWeight: FontWeight.w400,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildRegularDate(DateTime date) {
    return Container(
      width: 55.w,
      height: 55.h,
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff71456F),
            fontWeight: FontWeight.w400,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }
}
